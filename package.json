{"name": "salla-ai-chatbot-integration", "version": "1.0.0", "description": "نظام تكامل الذكاء الاصطناعي مع متاجر سلة", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "jest", "install-deps": "npm install", "setup": "node scripts/setup.js"}, "keywords": ["salla", "ai", "chatbot", "ecommerce", "arabic", "integration"], "author": "Augment Agent", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}